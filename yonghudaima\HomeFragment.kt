package com.huobi.home.ui

import android.Manifest
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.DecelerateInterpolator
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.LinearInterpolator
import android.view.animation.OvershootInterpolator
import android.widget.CheckBox
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.Keep
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout.LayoutParams
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import androidx.viewpager2.widget.ViewPager2
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.business.common.red_packet.RedPacketManager
import com.google.android.material.appbar.AppBarLayout
import com.google.zxing.client.android.CaptureActivity
import com.hbg.lib.common.utils.DateTimeUtils
import com.hbg.lib.common.utils.DebugLog
import com.hbg.lib.common.utils.HUIHandler
import com.hbg.lib.common.utils.SP
import com.hbg.lib.common.utils.crypt.MD5Utils
import com.hbg.lib.core.BaseModuleConfig
import com.hbg.lib.core.constants.ConfigConstant
import com.hbg.lib.core.model.ContractOverviewController
import com.hbg.lib.core.network.rx.EasySubscriber
import com.hbg.lib.core.page.SmartRefreshFooter
import com.hbg.lib.core.permissions.EasyPermissions
import com.hbg.lib.core.permissions.EasyPermissions.PermissionCallbacks
import com.hbg.lib.core.ui.BaseActivity
import com.hbg.lib.core.util.AppLanguageHelper
import com.hbg.lib.core.util.NightHelper
import com.hbg.lib.core.util.PhoneUtils
import com.hbg.lib.core.util.RxJavaHelper
import com.hbg.lib.core.util.ThemeHelper
import com.hbg.lib.core.webview.HBBaseWebActivity
import com.hbg.lib.network.hbg.HbgGlobalApi
import com.hbg.lib.network.hbg.core.bean.TokenBindInfo
import com.hbg.lib.network.linear.swap.controller.LinearSwapOverviewController
import com.hbg.lib.network.pro.HbgProApi
import com.hbg.lib.network.pro.core.bean.ProTokenUpdate
import com.hbg.lib.network.pro.socket.listener.LastKlineListener
import com.hbg.lib.network.pro.socket.listener.MarketOverviewListenerV2
import com.hbg.lib.network.pro.socket.response.LastKlineResponse
import com.hbg.lib.network.pro.socket.response.MarketOverviewV2Response
import com.hbg.lib.network.retrofit.exception.APIStatusErrorException
import com.hbg.lib.network.swap.core.controller.SwapOverviewController
import com.hbg.lib.widgets.utils.HuobiToastUtil
import com.hbg.module.content.ui.fragment.NewsChildFragment
import com.hbg.module.huobi.im.RedPoint.RedPointHelper
import com.hbg.module.libkt.base.adapter.BasePageAdapter
import com.hbg.module.libkt.base.ext.toGsonStr
import com.hbg.module.libkt.custom.indicator.CoIndicator
import com.hbg.module.libkt.custom.indicator.IndicatorHelper
import com.hbg.module.libkt.custom.indicator.TabClickListener
import com.hbg.module.libkt.custom.indicator.TabData
import com.hbg.module.libkt.custom.indicator.navigator.CommonNavigator
import com.hbg.module.libkt.custom.indicator.navigator.titles.RedPointPagerTitleView
import com.hbg.module.libkt.utils.event.LiveDataBus.with
import com.hbg.module.libkt.utils.event.LiveKeyCons
import com.hbg.module.livesquare.ui.LiveSquareHomeFragment
import com.hbg.module.livesquare.utils.LiveTrackUtils
import com.huobi.R
import com.huobi.account.event.LogOutEvent
import com.huobi.account.helper.UserProvider
import com.huobi.account.ui.SecuritySetActivity
import com.huobi.account.ui.VerificationStartActivity
import com.huobi.apm.AppStartMonitorManager
import com.huobi.edgeengine.EdgeEngine
import com.huobi.edgeengine.node.DataCallback
import com.huobi.finance.api.RiskService
import com.huobi.finance.bean.TsvMsg
import com.huobi.home.data.HomeContentTabRedPointEvent
import com.huobi.home.data.HomepageConfig
import com.huobi.home.data.TransferAmountInfo
import com.huobi.home.engine.HomeBridgeAbility
import com.huobi.home.engine.HomeEngineCore
import com.huobi.home.engine.HomeEngineEvent
import com.huobi.home.presenter.HomePresenter
import com.huobi.homemarket.model.ProOverviewController
import com.huobi.index.bean.IndexInformationRequestData
import com.huobi.index.bean.IndexInformationRequestData.ACTION_REFRESH
import com.huobi.index.bean.IndexInformationRequestData.ACTION_UP
import com.huobi.index.helper.IndexHelper
import com.huobi.index.helper.LoginTokenHelper
import com.huobi.index.trace.IndexLifeCycleStep
import com.huobi.index.trace.IndexLifeCycleTracer
import com.huobi.index.ui.AnnouncementFragment
import com.huobi.index.ui.FeedFragment
import com.huobi.index.ui.ScanLoginSuccessActivity
import com.huobi.login.bean.IAuthTarget
import com.huobi.login.usercenter.external.UserCenterActionHelper
import com.huobi.main.helper.HBHomeSkinHelper
import com.huobi.main.navigator.JumpUtils
import com.huobi.page.SmartRefreshHeader
import com.huobi.retrofit.HRetrofit
import com.huobi.statistics.SensorsDataHelper
import com.huobi.utils.ScanUtils
import com.huobi.view.MyNestedScrollView
import com.scwang.smartrefresh.layout.SmartRefreshLayout
import com.scwang.smartrefresh.layout.api.RefreshLayout
import com.scwang.smartrefresh.layout.constant.RefreshState
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener
import com.scwang.smartrefresh.layout.listener.SimpleMultiPurposeListener
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.Serializable
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.abs

/**
 *@Description:
 *@Author: wangyuxi
 *@Date: 2023/4/13
 */
class HomeFragment : BaseHomeFragment<HomePresenter, HomePresenter.HomeUI>(), HomePresenter.HomeUI, PermissionCallbacks {

    val TAG = "HomeFragment"

    // webview pool container
    private var mWebviewPoolContainer: FrameLayout? = null

    //navigation 区域
    private var mNavigation: RelativeLayout? = null
    private var mNavigationContainer: LinearLayout? = null

    //下拉刷新
    private var mRefreshLayout: SmartRefreshLayout? = null

    //下拉刷新头
    private var mCustomHeader: SmartRefreshHeader? = null

    //滚动组件
    private var mFluentScrollView: MyNestedScrollView? = null
    private var clLayout: CoordinatorLayout? = null
    private var appBarLayout: AppBarLayout? = null

    //    private var mFluentScrollerLayout: ConsecutiveScrollerLayout? = null
    private var mFluentLayout: LinearLayout? = null

    //内容feed
    private var coIndicator: CoIndicator? = null
    private var mFeedViewPager: ViewPager2? = null
    private var mFeedTabLayoutGroup: LinearLayout? = null
    private var mFeedReleaseBtn: View? = null
    private var feedFragmentList: ArrayList<Fragment>? = null
    private var homeRefreshIcon: ImageView? = null
    //行情数据监听
    private var mProListener: MarketOverviewListenerV2? = null

    //端引擎
    private var mEdgeEngine: EdgeEngine? = null

    private var homepageConfig: HomepageConfig? = null
    private var transferAmountInfo: TransferAmountInfo? = null

    private var homeAnimation: ViewGroup? = null

    private var isTopIcon = false
    private var homeRadioContainer: ViewGroup? = null
    private var homeCheckBox: CheckBox? = null
    private var homeCheckBoxBg: CheckBox? = null
    private var homeCheckBoxIcon: CheckBox? = null
    private var animator: ValueAnimator? = null
    private var mScrollY = 0
    private var mIsGetProToken = false
    private var feedScroll = false
    private var lastScrollUpdate: Long = -1

    private var nowCurrentContentPos = -1
    var tabs: ArrayList<TabData>? = null

    // 吸底Tab相关变量
    private var stickyBottomTabContainer: LinearLayout? = null
    private var stickyCoIndicator: CoIndicator? = null  // 吸底CoIndicator实例
    private var isBottomTabVisible = false
    private var isAnimating = false
    private var isInitialized = false  // 添加初始化标志
    private var isHandlingTabClick = false  // 添加点击处理标志，防止滚动监听器干扰
    private var navigationBarHeight: Int = 0
    private var actualBottomMargin: Int = 0
    private val animationDuration: Long = 250L  // 优化动画时长，提升响应速度
    private val scrollToPosition: Float = 0.33f  // 滚动到屏幕的1/3位置

    // 自然切换相关变量
    private var lastSwitchTime = 0L  // 上次切换时间，用于防抖动
    private val switchDebounceDelay = 500L  // 切换防抖动延迟（毫秒），增加到500ms避免频繁切换

    // 预显示机制相关变量
    private var isPreShowing = false  // 是否处于预显示状态
    private var preShowAnimator: Animator? = null  // 预显示动画器

    // 动态阈值算法相关变量
    private var lastScrollY = 0  // 上次滚动位置
    private var lastScrollTime = 0L  // 上次滚动时间
    private var scrollVelocity = 0f  // 滚动速度 (px/ms)

    // 预加载机制相关变量
    private var isPreCreated = false  // 是否已预创建
    private var preCreatedContainer: LinearLayout? = null  // 预创建的容器
    private var preCreatedCoIndicator: CoIndicator? = null  // 预创建的CoIndicator
    private var showRedPoint:Boolean =false
    private var animated: Boolean = false
    private var showAnimated: Boolean = false


    override fun createView(inflater: LayoutInflater, container: ViewGroup, savedInstanceState: Bundle?): View {
        val layoutRes: Int = getAppropriateLayoutResId()
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
        //啄木鸟首页启动埋点
        IndexLifeCycleTracer.getInstance().report(IndexLifeCycleStep.AppHomePage)
        HomeEngineCore.navModuleViews.clear()
        HomeEngineCore.fluentModuleViews.clear()
        return ThemeHelper.instance.getNewThemeView(requireActivity(), inflater, layoutRes, container)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        setRootViewOnly(null)
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        presenter.getHomePageConfig()
        presenter.refreshContract()
    }

    override fun getUI(): HomePresenter.HomeUI {
        return this
    }

    override fun createPresenter(): HomePresenter {
        return HomePresenter()
    }

    @SuppressLint("RestrictedApi")
    override fun initViews() {
        mWebviewPoolContainer = viewFinder.find(R.id.webview_pool_container)

        mNavigation = viewFinder.find(R.id.navigation_container)
        mNavigationContainer = viewFinder.find(R.id.home_navigation_ll)
        val statusBar = viewFinder.find<View>(R.id.home_status_bar)
        val params = statusBar.layoutParams
        params.height = BaseActivity.getStatusBarHeight(statusBar.context)
        statusBar.layoutParams = params

        mRefreshLayout = viewFinder.find(R.id.fluent_refresh_layout)
        clLayout = viewFinder.find(R.id.clLayout)
        appBarLayout = viewFinder.find(R.id.appBarLayout)
        homeRefreshIcon = viewFinder.find(R.id.home_refresh_icon)
        // 初始化吸底Tab相关配置
        initStickyBottomTab()
//        mFluentScrollerLayout = viewFinder.find(R.id.fluent_scrollerlayout)
        mFluentScrollView = viewFinder.find(R.id.fluent_content_nsv)
        mFluentLayout = viewFinder.find(R.id.fluent_container)

        homeAnimation = viewFinder.find(R.id.rl_new_hand_area_animation_layer)
        homeRefreshIcon?.setOnClickListener {
            refreshFeedData(ACTION_REFRESH)
        }
        initEngine()
        initViewPager()
        initWifiInfo(activity)
        setRefreshLayout()

        mRefreshLayout?.setOnMultiPurposeListener(object : SimpleMultiPurposeListener() {

        })

        activity?.let {
            homeRadioContainer = it.findViewById(R.id.main_home_tab)
            homeRadioContainer?.run {
                homeCheckBox = this.findViewById(R.id.main_index_cb)
                homeCheckBoxBg = this.findViewById(R.id.main_index_cb_bg)
                homeCheckBoxIcon = this.findViewById(R.id.main_index_cb_icon)
                val remoteSkinBean = HBHomeSkinHelper.getInstance().getRealRemoteSkinBean(it)
                val isNightMode = NightHelper.getInstance().isNightMode
                remoteSkinBean?.let { bean ->
                    bean.tabbar?.let { bar ->
                        val tabBarBean = if (isNightMode) {
                            bar.night
                        } else {
                            bar.light
                        }
                        tabBarBean?.let {
                            val homeIcon = tabBarBean.home.icon
                            val rocketIcon = tabBarBean.rocket.rocket_icon
                            val rocketBg = tabBarBean.rocket.rocket_bg
                            homeCheckBoxBg?.buttonDrawable = HBHomeSkinHelper.getInstance()
                                .getDrawable(R.drawable.tab_bg_feed_top_bg, rocketBg, homeIcon)
                            homeCheckBoxIcon?.buttonDrawable = HBHomeSkinHelper.getInstance()
                                .getDrawable(R.drawable.tab_bg_feed_top_icon, rocketIcon, homeIcon)
                        }
                    }
                }
            }
        }

        AppStartMonitorManager.getInstance().mainAppear();
    }

    fun checkFutureSub() {

    }

    /**
     * 合约行情ws订阅
     */
    private fun subscribeFutureMarketWebSocket() {
        DebugLog.i("FutureRank", "subscribeFutureMarketWebSocket")
        ContractOverviewController.getInstance().subOverview()
        SwapOverviewController.getInstance().subOverview()
        LinearSwapOverviewController.getInstance().subOverview()
    }

    /**
     * 合约行情ws退订
     */
    private fun unsubscribeFutureMarketWebSocket() {
        DebugLog.i("FutureRank", "unsubscribeFutureMarketWebSocket")
        ContractOverviewController.getInstance().unSubOverView()
        SwapOverviewController.getInstance().unSubOverView()
        LinearSwapOverviewController.getInstance().unSubOverView()
    }

    override fun addEvent() {
//        registMarketListener()
        registLogin()
        homeIconEvent()
    }


    override fun afterInit() {
    }


    override fun onDestroyView() {
        mFeedViewPager?.adapter = null
        // 清理吸底Tab
        cleanupStickyBottomTab()
        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
        if (mEdgeEngine != null) {
            mEdgeEngine!!.release()
            mEdgeEngine = null
        }
//        unRegistMarketListener()
        stopUpdateRankingTimer()
        unregistRankListener()
    }

    private fun getAppropriateLayoutResId(): Int {
        SP.set(IndexHelper.KEY_HOME_PAGE_LAYOUT_TYPE, "A")
        return R.layout.fragment_home
    }

    /***--------------Engine----------------------***/
    fun initEngine() {
        mEdgeEngine = EdgeEngine(requireActivity(), "home")
        mEdgeEngine?.registerAbility("homeBridge", HomeBridgeAbility::class.java)
//        mEdgeEngine?.registerWidget(AnimTextViewWidget.KEY, AnimTextViewWidget::class.java)
        mEdgeEngine?.registerWidget(HomeAssetTextView.KEY, HomeAssetTextView::class.java)
        mEdgeEngine?.registerWidget(HomeHBHomeCubeLivingView.KEY,HomeHBHomeCubeLivingView::class.java)
        mEdgeEngine?.registerWidget(HomeCubeCompositeView.KEY,HomeCubeCompositeView::class.java)
        mEdgeEngine?.runScript()
        mEdgeEngine?.sendEvent("initEngine()")
        sendCommonEvents()
        initRankListener()
    }

    fun sendCommonEvents() {
        HomeEngineEvent.sendSymbolInfo(mEdgeEngine)
        HomeEngineEvent.sendLoginStatus(mEdgeEngine)
        HomeEngineEvent.sendCommonConfig(mEdgeEngine)
        HomeEngineEvent.sendRateTypeStr(mEdgeEngine)

        HomeEngineEvent.isConditionMet.observe(this) { isTrue ->
            if (isTrue) {
                // 执行后续操作
                HomeEngineCore.refreshHomeView(mEdgeEngine,activity)
            }
        }
    }

    /***--------------UI----------------------***/
    override fun lodaHomeUI(homepageConfig: HomepageConfig?, userinfo: TransferAmountInfo?) {
        if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
            return
        }

        this.transferAmountInfo = userinfo
        if (this.homepageConfig == null) {
            Log.d("Home", "homepageConfig == null")
            this.homepageConfig = homepageConfig
            mFluentLayout?.removeAllViews()
            mNavigationContainer?.removeAllViews()
            HomeEngineCore.startRenderHome(
                mEdgeEngine,
                requireActivity(),
                mNavigationContainer,
                mFluentLayout,
                homepageConfig,
                userinfo
            )
        } else {
            this.homepageConfig = homepageConfig
            HomeEngineCore.refreshHomeView(
                mEdgeEngine,
                requireActivity(),
                mNavigationContainer,
                mFluentLayout,
                homepageConfig,
                userinfo
            )
            mRefreshLayout?.finishRefresh()
        }
    }

    override fun sendSymbolInfo() {
        HomeEngineEvent.sendSymbolInfo(mEdgeEngine)
    }

    /**
     * 设置刷新
     */
    fun setRefreshLayout() {
        mRefreshLayout?.isEnableRefresh = true
        mRefreshLayout?.isEnableLoadMore = true
        mRefreshLayout?.setEnableLoadMoreWhenContentNotFull(false)
        mRefreshLayout?.setRefreshFooter(SmartRefreshFooter(activity))
        mCustomHeader = SmartRefreshHeader(activity)
        mRefreshLayout?.setRefreshHeader(mCustomHeader!!)
        mRefreshLayout?.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onLoadMore(refreshLayout: RefreshLayout) {
                DebugLog.i("home -----加载更多....")
                refreshFeedData(ACTION_UP)

            }

            override fun onRefresh(refreshLayout: RefreshLayout) {
                sendCommonEvents()
                //页面可见状态下拉刷新请求数据，否则停止刷新
                if (ui.isCanBeSeen) {
                    presenter.getHomePageConfig()
                    refreshFeedData(ACTION_REFRESH)

                } else {
                    ptrRefreshFinished(true)
                }
            }
        })
    }

    fun ptrRefreshFinished(refresh: Boolean) {
        val nowTime = DateTimeUtils.getDateTimeNow()
        val strTime = DateTimeUtils.formatStr(nowTime, DateTimeUtils.FORMAT_MM_DD_HH_MM_SS)
        mCustomHeader!!.setLastUpdateText(strTime)
        if (refresh) {
            mRefreshLayout?.finishRefresh()
            mRefreshLayout?.setNoMoreData(false)
        } else {
            mRefreshLayout?.finishLoadMore()
        }
    }

    fun isRefreshing(): Boolean {
        return mRefreshLayout?.isRefreshing ?: false
    }

    fun setRefreshState() {
        mRefreshLayout?.finishLoadMore()
    }

    private fun isLoading(): Boolean {
        return mRefreshLayout?.state == RefreshState.Loading
    }

    private var mRankCache: ConcurrentHashMap<String, LastKlineResponse> = ConcurrentHashMap()
    private var mLastSymbols = mutableListOf<String>()
    private var mRankListener: LastKlineListener? = null
    private var mUpdateRankingInterval = 0L//0=不限制，1=限制频率1秒更新1次
    private fun initRankListener() {
        mEdgeEngine?.registerDataCallback("subRankingData", object : DataCallback {
            override fun onCallback(value: Any?) {
                try {
                    if (value == null) return
                    Log.d("homeFragment", "subRankingData value= ${value}")
                    val jsonObject = JSON.parseObject(value.toString())
                    if (jsonObject.containsKey("sub")) {
                        val subList: List<String> =
                            jsonObject.getJSONArray("sub").toJavaList(String::class.java)
                        if (subList.equals(mLastSymbols)) {
                            Log.d("homeFragment", "subRankingData subList is same, no need to update")
                            return
                        }
                        unregistRankListener()
                        mLastSymbols.clear()
                        mLastSymbols.addAll(subList)
                        registRankListener()
                    }
                } catch (e: Throwable) {
                    e.printStackTrace()
                }
            }
        })
        mEdgeEngine?.registerDataCallback("updateRankingInterval", object : DataCallback {
            override fun onCallback(value: Any?) {
                try {
                    Log.d("homeFragment", "updateRankingInterval value= ${value}")
                    if (value == null) return
                    val interval: Float? = value.toString().toFloatOrNull()
                    if (interval == null) return
                    val temp = (interval * 1000L).toLong()
                    if (mUpdateRankingInterval == temp) return
                    mUpdateRankingInterval = temp
                    if (mUpdateRankingInterval > 0) {
                        startUpdateRankingTimer()
                    }
                } catch (e: Throwable) {
                    e.printStackTrace()
                }
            }
        })
    }
    private var mTimer: Job? = null
    private fun startUpdateRankingTimer() {
        stopUpdateRankingTimer()
        mTimer = lifecycleScope.launch {
            while (true) {
                if (mRankCache.isNotEmpty()) {
                    val result = JSONObject()
                    for ((key, resp) in mRankCache) {
                        if (resp == null) continue
                        val item = JSONObject()
                        item["decimalcPrice"] = resp.tick.close
                        item["decimalDelta"] = resp.tick.changeRatio * 100
                        item["strAmount"] = resp.tick.amount
                        item["symbol"] = resp.symbol
                        result[resp.symbol] = item
                    }
                    HomeEngineEvent.sendSocketData(mEdgeEngine, "market", result)
                }
                delay(mUpdateRankingInterval)
            }
        }
    }
    private fun stopUpdateRankingTimer() {
        if (mTimer != null && mTimer?.isActive == true) {
            mTimer?.cancel()
        }
    }

    private fun registRankListener() {
        if (mLastSymbols.isEmpty()) {
            return
        }
        if (mUpdateRankingInterval > 0 && mTimer?.isActive != true) {
            startUpdateRankingTimer()
        }
        if (mRankListener == null) {
            mRankListener = object : LastKlineListener() {
                override fun onSuccess(response: LastKlineResponse) {
                    mRankCache.put(response.symbol, response)
                    //间隔大于0，走定时器更新
                    if (mUpdateRankingInterval > 0L) return
                    val result = JSONObject()
                    val item = JSONObject()
                    item["decimalcPrice"] = response.tick.close
                    item["decimalDelta"] = response.tick.changeRatio * 100
                    item["strAmount"] = response.tick.amount
                    item["symbol"] = response.symbol
                    result[response.symbol] = item
                    HomeEngineEvent.sendSocketData(mEdgeEngine, "market", result)
                }
            }
        }
        HbgProApi.getAPI().subscribeBatch(
            true,
            mLastSymbols,
            mRankListener
        )
    }

    private fun unregistRankListener() {
        if (mLastSymbols.isEmpty()) {
            return
        }
        HbgProApi.getAPI().subscribeBatch(false, mLastSymbols, mRankListener)
        mRankCache.clear()
    }

    private fun initRedPointListener() {
        RedPointHelper.getInstance().setChatListRedPointUiUpdateListener { baseRedPointNode ->
            val redCount = baseRedPointNode.redCount
            HomeEngineEvent.sendUnreadMessage(mEdgeEngine, redCount)
        }
    }

    private fun registLogin() {
        //登录成功回调
        with(LiveKeyCons.HOME_FEED_REFRESH, Int::class.java).observe(this) { integer: Int? ->
            if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
                // is nothing todo it
            } else {
                HomeEngineEvent.sendLoginStatus(mEdgeEngine)
                presenter.getTransferAmountInfo()
                HomeEngineCore.refreshHomeView(mEdgeEngine, requireActivity())
            }
        }

        with(LiveKeyCons.HOME_TAB_CHANGE, Int::class.java).observe(this) { index: Int? ->
            if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
                // is nothing todo it
            } else {
                index?.let {
                    mFeedViewPager?.setCurrentItem(index, false)
                }
            }
        }

        with(LiveKeyCons.HOME_CONTENT_TAB_RED_POINT, HomeContentTabRedPointEvent::class.java).observeStick(
            this,
            { event: HomeContentTabRedPointEvent? ->
                Log.d("HomeFragment", "${LiveKeyCons.HOME_CONTENT_TAB_RED_POINT} event : ${event.toGsonStr}")
                if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
                    // is nothing
                } else {
                    event?.let { it ->
                        changeRedPoint(it.index, it.isShow)
                        showRedPoint = it.isShow
                    }
                }
            })
    }

    /**
     * 滑动状态处理
     */
    private val scrollStateHandler: Runnable = object : Runnable {
        override fun run() {
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastScrollUpdate > 100) {
                lastScrollUpdate = -1
                feedScroll = false
            } else {
                HUIHandler.getInstance().postDelayed(this, 100)
            }
        }
    }

    fun isScroll(): Boolean {
        return feedScroll
    }

    private var viewHeight = 0
    private var verticalOffset = 0

    //  Feed流高度
    private var feedViewPageHeight = 0
    private fun homeIconEvent() {
        mFluentScrollView?.viewTreeObserver?.addOnGlobalLayoutListener {
            if (mFluentScrollView?.height ?: 0 > 0) {
                viewHeight = mFluentScrollView!!.height
            }
        }
        appBarLayout?.addOnOffsetChangedListener { _, verticalOffset ->
            this.verticalOffset = abs(verticalOffset)
            if (viewHeight - this.verticalOffset < 8) {
                playTabIconAnimation(true)
            } else {
                playTabIconAnimation(false)
            }
            if (feedViewPageHeight == 0) {
                feedViewPageHeight = mFeedViewPager?.height ?: 0
            }
            if (feedViewPageHeight == 0) {
                feedViewPageHeight = mFeedViewPager?.height ?: 0
            }
            if (viewHeight != 0) {
                (feedFragmentList?.getOrNull(0) as? FeedFragment)?.let { feedFragment ->
                    if (viewHeight + verticalOffset == 0) {
                        //   Feed流吸顶
                        DebugLog.d(TAG, "当前吸顶了")
                        if(mNavigationContainer?.visibility == View.VISIBLE)
                        if (!animated && !showAnimated) {
                            mNavigationContainer?.let { disappearUp(it, 500) }
                        }
                        feedFragment.homeStickyHeaderListener(true)
                    } else {
                        //   Feed流未吸顶
                        DebugLog.d(TAG, "当前未吸顶")
                        if (mNavigationContainer?.visibility != View.VISIBLE) {
                            if (!animated && !showAnimated) {
                                mNavigationContainer?.let { appearDown(it, 500) }
                            }
                        }

                        feedFragment.homeStickyHeaderListener(false)
                    }
                }
            }

            // 检查吸底Tab的显示状态
            // 添加 isHandlingTabClick 检查，防止点击处理期间的干扰
            if (!isAnimating && isInitialized && !isHandlingTabClick) {
                checkStickyBottomTabVisibility()
            }
        }
    }


    private fun appearDown(view: View, duration: Long = 300) {
        // 如果视图是GONE状态，先设置为VISIBLE但保持不可见
        if (view.visibility == View.GONE) {
            view.visibility = View.VISIBLE
            view.alpha = 0f // 初始完全透明

            // 等待视图完成布局测量
            view.post {
                // 获取视图高度（此时已测量完成）
                val viewHeight = view.height.toFloat()

                // 设置初始位置：在屏幕上方（负高度）
                view.translationY = -viewHeight

                // 创建并执行动画
                createAndStartAnimation(view, -viewHeight, duration)
            }
        } else {
            // 如果视图已经是VISIBLE/INVISIBLE状态，直接获取高度
            if (view.height == 0) {
                view.post {
                    val viewHeight = view.height.toFloat()
                    view.translationY = -viewHeight
                    view.alpha = 0f
                    createAndStartAnimation(view, -viewHeight, duration)
                }
            } else {
                val viewHeight = view.height.toFloat()
                view.translationY = -viewHeight
                view.alpha = 0f
                createAndStartAnimation(view, -viewHeight, duration)
            }
        }
    }

    private fun createAndStartAnimation(view: View, startY: Float, duration: Long) {
        // 确保视图可见（可能在post过程中状态改变）
        view.visibility = View.VISIBLE

        // 创建移动动画：从起始位置滑到原始位置
        val moveAnim = ObjectAnimator.ofFloat(
            view,
            "translationY",
            startY,
            0f
        )

        // 创建淡入动画
        val fadeAnim = ObjectAnimator.ofFloat(
            view,
            "alpha",
            0f,
            1f
        )

        // 创建动画集合
        val animatorSet = AnimatorSet()
        animatorSet.playTogether(moveAnim, fadeAnim)
        animatorSet.duration = duration

        // 添加动画监听器
        animatorSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator) {
                showAnimated = true
            }

            override fun onAnimationEnd(animation: Animator) {
                showAnimated = false
                // 清除动画属性，避免影响布局
                view.translationY = 0f
                view.alpha = 1f
            }

            override fun onAnimationCancel(animation: Animator) {
                showAnimated = false
                // 确保视图最终状态正确
                view.translationY = 0f
                view.alpha = 1f
                view.visibility = View.VISIBLE
            }
        })

        // 启动动画
        animatorSet.start()
    }

    private fun disappearUp(view: View, duration: Long = 300) {
        // 创建平移动画（向上移动）
        val moveAnim = ObjectAnimator.ofFloat(view, "translationY", 0f, -view.height.toFloat())

        // 创建淡出动画
        val fadeAnim = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f)

        // 组合动画
        val animatorSet = AnimatorSet()
        animatorSet.playTogether(moveAnim, fadeAnim)
        animatorSet.duration = duration

        // 动画结束后隐藏 View（可选）
        animatorSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                animated = false
                view.visibility = View.GONE
                // 重置状态（如果需要重复使用）
                view.translationY = 0f
                view.alpha = 1f
            }
        })
        animated = true
        animatorSet.start()
    }

    /**
     * playTabIconAnimation
     *
     * @param topIcon topIcon
     */
    private fun playTabIconAnimation(topIcon: Boolean) {
        if (isTopIcon != topIcon) {
            isTopIcon = topIcon
            animator = ValueAnimator.ofFloat(0f, 1f)
            animator?.addUpdateListener {
                val scale = it.animatedValue as Float
                val scaleDiff = 1f - scale
                homeCheckBoxBg?.alpha = if (isTopIcon) {
                    scale
                } else {
                    scaleDiff
                }
                homeCheckBox?.alpha = if (isTopIcon) {
                    scaleDiff
                } else {
                    scale
                }
                homeCheckBox?.scaleX = if (isTopIcon) {
                    scaleDiff
                } else {
                    scale
                }
                homeCheckBox?.scaleY = if (isTopIcon) {
                    scaleDiff
                } else {
                    scale
                }
                homeCheckBoxIcon?.alpha = if (isTopIcon) {
                    scale
                } else {
                    scaleDiff
                }
                homeCheckBoxIcon?.translationY = if (isTopIcon) {
                    scaleDiff
                } else {
                    scale
                } * (homeCheckBoxIcon?.height ?: 1)
            }
            animator?.duration = 300
            animator?.start()
        }
    }

    /**
     * when the user logout, change userInfo view
     * @param event Logout event
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    @Keep
    fun onEvent(event: LogOutEvent?) {
        mIsGetProToken = false
        if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
            // is nothing todp is
            return
        }

        HomeEngineEvent.sendLoginStatus(mEdgeEngine)
        presenter.getTransferAmountInfo()
        HomeEngineCore.refreshHomeView(mEdgeEngine, requireActivity())
    }

    /**
     * 登录成功  generateUserSig接口不会触发ticket换token，所以此处添加了token的监听来请求generateUserSig接口
     *
     * @param event ProTokenUpdate
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    @Keep
    fun onProTokenUpdate(event: ProTokenUpdate) {
        val proToken = event.proToken
        Log.e("Home", "onProTokenUpdate proToken:$proToken getUI():$ui")
        if (!TextUtils.isEmpty(proToken) && ui != null) {
            presenter.getTransferAmountInfo()
        }
        HomeEngineEvent.sendLoginStatus(mEdgeEngine)
        HomeEngineEvent.sendUnreadMessage(mEdgeEngine)
        mIsGetProToken = true;
    }

    override fun refreshHomeData() {
        if (context == null || (context is FragmentActivity && (context as FragmentActivity).isFinishing)) {
            // is nothing todo it
            return
        }

        HomeEngineCore.refreshHomeView(mEdgeEngine, requireActivity())
    }

    override fun getWebviewContainer(): ViewGroup? {
        return mWebviewPoolContainer
    }

    override fun isGetProToken(): Boolean {
        return mIsGetProToken
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && requestCode == 1001) {
            val resultString = data?.getStringExtra(CaptureActivity.RESULT_STRING)
            DebugLog.i("onActivityResult resultString=$resultString")
            if (ScanUtils.isScanLogin(resultString)) {
                UserCenterActionHelper.getInstance().checkLogin(activity, AuthTarget())
            } else if (ScanUtils.isScanFace(resultString)) {
                //安全扫码
                val uri = Uri.parse(resultString)
                val tsvToken = uri.getQueryParameter("tsvToken")
                DebugLog.i(SecuritySetActivity.TAG_QR_SCAN, "tsvToken=$tsvToken")
                if (checkSign(uri)) {
                    if (tsvToken != null) {
                        requestTsvMsg(tsvToken)
                    } else {
                        requestTsvMsg("")
                    }
                } else {
                    DebugLog.e(SecuritySetActivity.TAG_QR_SCAN, "checkSign Failed.")
                    HuobiToastUtil.showErrorShort(R.string.n_qr_scan_not_available)
                }
            } else if (ScanUtils.isScanOpen(resultString)) {
                try {
                    val redPacketResult = RedPacketManager.isRedPacket(resultString)
                    if (redPacketResult.isRedPacket) {
                        val codeWord = redPacketResult.codeWord
                        val aty = activity
                        if (null != aty) {
                            BaseModuleConfig.getCallback().checkLoginByRedPacket(aty, codeWord)
                        }
                    } else {
                        JumpUtils.getInstance().tryPushJumpUrl(Uri.parse(resultString)).checkIt().consumeJumpUrl()
                    }
                } catch (e: java.lang.Exception) {
                    e.printStackTrace()
                    HuobiToastUtil.showErrorShort(R.string.n_qr_scan_not_available)
                }
            } else if (!TextUtils.isEmpty(resultString) && resultString!!.startsWith("file:///android_asset/")) {
                HBBaseWebActivity.showWebView(activity, resultString, null, null, false)
            } else {
                //扫描内容无效提示
                HuobiToastUtil.showErrorShort(R.string.n_qr_scan_not_available)
            }
        }
    }

    /**
     * 检查签名
     *
     * @param uri 扫码得到的uri
     * @return 签名通过
     */
    private fun checkSign(uri: Uri): Boolean {
        val ts = uri.getQueryParameter("ts")
        val sign = uri.getQueryParameter("sign")
        val localSign = MD5Utils.getMD5((ts + "Verify").toByteArray())
        return sign != null && sign.equals(localSign, ignoreCase = true)
    }

    /**
     * 请求提示语
     *
     * @param tsvToken 上个页面扫码获取的token
     */
    fun requestTsvMsg(tsvToken: String) {
        DebugLog.i(SecuritySetActivity.TAG_QR_SCAN, "requestTsvMsg")
        val param = HashMap<String, Any>()
        param["tsvToken"] = tsvToken
        param["uid"] = UserProvider.getInstance().currentId
        param["lang"] = AppLanguageHelper.getInstance().curLanguageHeader
        HRetrofit.risk<RiskService>(RiskService::class.java)
            .getTsvMsg(param)
            .compose<TsvMsg>(HRetrofit.riskIntCodeTransformer<TsvMsg>())
            .compose<TsvMsg>(RxJavaHelper.observeOnMainThread<TsvMsg>(ui))
            .subscribe(object : EasySubscriber<TsvMsg>() {
                override fun onStart() {
                    super.onStart()
                    <EMAIL>()
                }

                override fun onNext(tsvMsg: TsvMsg) {
                    super.onNext(tsvMsg)
                    HUIHandler.getInstance().postDelayed({ VerificationStartActivity.start(activity, tsvToken, tsvMsg.tsvMsg) }, 20)
                }

                override fun onAfter() {
                    super.onAfter()
                    <EMAIL>()
                }
            })
    }

    /**
     * 初始化 wifi 信息 用于安全信息采集等 Android 9.0 需求权限
     *
     * @param activity 上下文
     */
    fun initWifiInfo(activity: Activity?) {
        if (PhoneUtils.isCurVersionAboveAndroidP()) {
            val perms = arrayOf(Manifest.permission.ACCESS_FINE_LOCATION)
            if (EasyPermissions.hasPermissions(context, *perms)) {
                PhoneUtils.getWifiSsid(activity)
                PhoneUtils.getMacAddress(activity)
                PhoneUtils.getNetworkType(activity)
            } else {
                EasyPermissions.requestPermissions(this, ConfigConstant.RC_FINE_LOCATION, *perms)
            }
        } else {
            PhoneUtils.getWifiSsid(activity)
            PhoneUtils.getMacAddress(activity)
            PhoneUtils.getNetworkType(activity)
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this)
    }

    override fun onPermissionsGranted(requestCode: Int, perms: List<String?>?) {
        if (ConfigConstant.RC_FINE_LOCATION == requestCode) {
            PhoneUtils.getWifiSsid(activity)
            PhoneUtils.getMacAddress(activity)
            PhoneUtils.getNetworkType(activity)
        }
    }

    override fun onPermissionsDenied(requestCode: Int, perms: List<String?>) {
        DebugLog.i("onPermissionsGranted requestCode=$requestCode perms=$perms")
    }

    /**---------------------------feed--------------------------------**/
    private fun initViewPager() {
        coIndicator = viewFinder.find(R.id.coIndicator)
        mFeedTabLayoutGroup = viewFinder.find(R.id.home_feed_linear_tabLayout)
        mFeedViewPager = viewFinder.find(R.id.home_viewPager)
//        mFeedViewPager?.isSaveEnabled = false
        feedFragmentList = arrayListOf(
            FeedFragment.newInstance(FeedFragment.TAB_TYPE_RECOMMEND),
            FeedFragment.newInstance(FeedFragment.TAB_TYPE_FOLLOW),
            NewsChildFragment.getInstance(-100, "7*24"),
            LiveSquareHomeFragment.newInstance(-100, ""),
            AnnouncementFragment.newInstance()
        )
        tabs = arrayListOf(
            TabData(resources.getString(R.string.n_content_found), 0, 0),
            TabData(resources.getString(R.string.n_content_communityList_attention), 1, 1),
            TabData(resources.getString(R.string.n_content_newsflash), 2, 2),
            TabData(resources.getString(R.string.n_live), 3, 3),
            TabData(resources.getString(R.string.n_notice), 4, 4)
        )
        val adapter = BasePageAdapter(this)
        adapter.addDatas(feedFragmentList!!)
        mFeedViewPager?.adapter = adapter
        mFeedViewPager?.offscreenPageLimit = 1
        activity?.let {
            IndicatorHelper.initBottomNoLineIndicator(
                it,
                tabs!!,
                coIndicator!!,
                0f,
                mFeedViewPager!!,
                16f,
                R.attr.Text_L1,
                R.attr.Text_L3,
                scaleSize = 20f,
                isBold = true,
                onTabClick = object : TabClickListener {
                    override fun onTabClick(index: Int) {
                        if (nowCurrentContentPos != -1 && nowCurrentContentPos == index) {
                            refreshFeedData(ACTION_REFRESH)
                        }
                        nowCurrentContentPos = index
                    }
                }
            )
            coIndicator?.listener = object : CoIndicator.PageSelectListener {
                override fun onSelected(position: Int) {
                    val item = tabs?.get(position)
                    if (0 == item?.type) {
                        SensorsDataHelper.track("app_community_homepage_find_tab", hashMapOf<String, String>())
                    } else if (2 == item?.type) {
                        SensorsDataHelper.newTrack("app_community_kxtab_click", hashMapOf<String, String>())
                    } else if (3 == item?.type) {
                        SensorsDataHelper.newTrack(LiveTrackUtils.LIVE_SQUARE_SHOW)
                    } else if (4 == item?.type) {
                        SensorsDataHelper.newTrack("app_bulletin_tab_click", hashMapOf<String, String>())
                        changeRedPoint(4, false)
                        showRedPoint = false
                    }

                    // 同步吸底Tab的选中状态
                    syncStickyTabSelection(position)
                }
            }
        }

        with(LiveKeyCons.HOME_FEED_REFRESH, Int::class.java).observe(this) { refreshFeedData(ACTION_REFRESH) }

        coIndicator?.post { refreshFeedData(IndexInformationRequestData.ACTION_REFRESH) }
        // 增加FeedTablayout Group 事件
        addFeedTabListener()
    }

    private fun addFeedTabListener() {
    }

    override fun switchTabRefreshData(actionType: Int) {
        super.switchTabRefreshData(actionType)
        setRefreshState()
    }

    override fun refreshFeedPrevPage(position: Int) {
        super.refreshFeedPrevPage(position)
        setRefreshState()
    }

    override fun refreshFeedData(actionType: Int) {
        val fragment = feedFragmentList?.get(mFeedViewPager?.currentItem ?: 0)
        fragment?.let {
            when (it) {
                is FeedFragment -> {
                    it.refreshData(actionType)
                    return
                }

                is NewsChildFragment -> {
                    if (actionType == ACTION_REFRESH) {
                        it.onRefresh(null)
                    }
                }

                is LiveSquareHomeFragment -> {
                    if (actionType == ACTION_REFRESH) {
                        it.refreshPage()
                    }
                }
                is AnnouncementFragment ->{
                    if (actionType == ACTION_REFRESH) {
//                        it.refreshPage()
                    }
                }

                else -> {}
            }
        }
        setRefreshState()

    }

    override fun onVisibleChangedFinal(visible: Boolean) {
        super.onVisibleChangedFinal(visible)
        if(visible){
            HomeEngineEvent.sendLoginStatus(mEdgeEngine)
            presenter.getTransferAmountInfo()
            HomeEngineCore.refreshHomeView(mEdgeEngine, requireActivity())
            ProOverviewController.getInstance().subOverview()
        }else{
            ProOverviewController.getInstance().unSubOverViewConditional()
        }
        checkFutureSub()
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onVisibleChanged(visible: Boolean) {
        super.onVisibleChanged(visible)
        if (visible) {
            HomeEngineEvent.sendCommonConfig(mEdgeEngine)
            HomeEngineEvent.sendRateTypeStr(mEdgeEngine)
            HomeEngineEvent.sendPageAppear(mEdgeEngine)
//            Log.d("home","当前的单位："+HomeEngineEvent.checkPricingMethodChanged())
//            if(HomeEngineEvent.checkPricingMethodChanged()){

//            }
//            presenter.getHomePageConfig()
//            presenter.updateHomeUI()
            homeRadioContainer?.setOnTouchListener { _, _ ->
                if (!feedScroll && verticalOffset >= (mFluentScrollView?.height ?: 1)) {
                    setRefreshState()
                    val behavior = (appBarLayout?.layoutParams as LayoutParams?)?.behavior as AppBarLayout.Behavior?
                    behavior?.setTopAndBottomOffset(0)
                    appBarLayout?.setExpanded(true, true)
                    HUIHandler.getInstance().postDelayed({ refreshFeedData(ACTION_REFRESH) }, 300)
                }
                false
            }
            initRedPointListener()
//            registMarketListener()
            registRankListener()
        } else {
            HomeEngineEvent.sendPageDisappear(mEdgeEngine)
            homeRadioContainer?.setOnTouchListener(null)
//            unRegistMarketListener()
            stopUpdateRankingTimer()
            unregistRankListener()
        }
    }

    /**
     * Tab红点 - 同时更新原生Tab和吸底CoIndicator的红点状态
     * @param index Int
     * @param isShow Boolean
     */
    private fun changeRedPoint(index: Int, isShow: Boolean = false) {
        try {
            // 更新原生Tab的红点
            (coIndicator?.navigator as? CommonNavigator)?.let { commonNavigator ->
                val titleView = commonNavigator.getPagerTitleView(index) as? RedPointPagerTitleView
                if (isShow) {
                    titleView?.showPoint()
                } else {
                    titleView?.hidePoint()
                }
            }

            // 同步更新吸底CoIndicator的红点状态
            updateStickyCoIndicatorRedPoint(index, isShow)

        } catch (e: Throwable) {
            e.printStackTrace()
            Log.d("HomeFragment", "changeRedPoint = $isShow , error = ${e.message ?: ""}")
        }
    }

    class AuthTarget : IAuthTarget, Serializable {
        override fun show(context: Context) {
            Log.d("Home", "---------show--------")
            LoginTokenHelper.getLoginTokenObservable().flatMap { s: String? ->
                HbgGlobalApi.getAPI().scanTokenBind(ScanUtils.getScanLoginCode(), 1, s).observable
            }.compose(RxJavaHelper.observeOnMainThread(null))
                .subscribe(object : EasySubscriber<TokenBindInfo>() {
                    override fun onStart() {
                        super.onStart()
//                        <EMAIL>()
                    }

                    override fun onNext(info: TokenBindInfo) {
                        super.onNext(info)
//                        <EMAIL>()
                        ScanLoginSuccessActivity.startScanLoginSuccessActivity(context, info)
                    }

                    override fun onError2(e: Throwable) {
                        super.onError2(e)
//                        <EMAIL>()
                    }

                    override fun onFailed(e: APIStatusErrorException) {
                        super.onFailed(e)
//                        <EMAIL>()
                    }
                })
        }

        constructor() {
        }

        companion object {
            private const val serialVersionUID = 5620774165925992463L
        }
    }

    // ==================== 吸底Tab功能实现 ====================

    /**
     * 初始化吸底Tab配置
     */
    private fun initStickyBottomTab() {
        // 检测导航栏高度
        detectNavigationBar()

        // 延迟启动滚动监听，确保布局完成
        clLayout?.post {
            clLayout?.postDelayed({
                isInitialized = true  // 标记为已初始化

                // 根据实际位置决定是否显示初始Tab
                checkAndShowInitialStickyBottomTab()
            }, 500) // 延迟500ms，确保布局完全完成
        }
    }

    /**
     * 清理吸底Tab资源 - 包括预创建和预显示状态
     */
    private fun cleanupStickyBottomTab() {
        try {
            // 清理显示状态的Tab
            if (isBottomTabVisible && stickyBottomTabContainer != null && clLayout != null) {
                clLayout?.removeView(stickyBottomTabContainer)
            }

            // 清理预显示状态的Tab
            if (isPreShowing && stickyBottomTabContainer != null && clLayout != null) {
                clLayout?.removeView(stickyBottomTabContainer)
                preShowAnimator?.cancel()
            }

            // 清理预创建的组件
            if (isPreCreated) {
                preCreatedContainer = null
                preCreatedCoIndicator = null
            }

            // 重置所有状态
            stickyBottomTabContainer = null
            stickyCoIndicator = null
            preShowAnimator = null
            isBottomTabVisible = false
            isPreShowing = false
            isPreCreated = false
            isInitialized = false

        } catch (e: Exception) {
        }
    }

    /**
     * 检测导航栏高度
     */
    private fun detectNavigationBar() {
        try {
            // 设置底部边距为0，让吸底Tab紧贴屏幕底部（CoordinatorLayout会自动处理导航栏）
            actualBottomMargin = 0

        } catch (e: Exception) {
            actualBottomMargin = 0
        }
    }

    /**
     * 检查设备是否有导航栏
     */
    private fun hasNavigationBar(): Boolean {
        val activity = activity ?: return false

        return try {
            val windowManager = activity.windowManager
            val display = windowManager.defaultDisplay
            val realDisplayMetrics = android.util.DisplayMetrics()
            val displayMetrics = android.util.DisplayMetrics()

            display.getRealMetrics(realDisplayMetrics)
            display.getMetrics(displayMetrics)

            // 如果真实高度大于显示高度，说明有导航栏
            realDisplayMetrics.heightPixels > displayMetrics.heightPixels
        } catch (e: Exception) {
            Log.w(TAG, "检测导航栏失败", e)
            false
        }
    }

    /**
     * 检查吸底Tab的显示状态 - 非对称切换逻辑，避免重叠显示
     *
     * 切换策略：
     * - 显示吸底Tab：当原生Tab大部分滚出屏幕时（底部超过 screenHeight - tabHeight）
     * - 隐藏吸底Tab：当原生Tab刚开始进入屏幕时（显示约20%时）就立即隐藏
     *
     * 这样可以避免原生Tab和吸底Tab同时显示的重叠情况
     */
    private fun checkStickyBottomTabVisibility() {
        if (isAnimating || coIndicator == null) {
            if (isAnimating) {
                Log.v(TAG, "跳过可见性检查：正在动画中")
            }
            return
        }

        // 如果正在处理Tab点击，跳过检查
        if (isHandlingTabClick) {
            Log.v(TAG, "跳过可见性检查：正在处理Tab点击")
            return
        }

        // 计算滚动速度用于动态阈值调整
        calculateScrollVelocity()

        // 检查CoIndicator是否已经完成布局
        if (coIndicator!!.height <= 0 || coIndicator!!.width <= 0) {
            return
        }

        // 获取CoIndicator的屏幕位置信息
        val location = IntArray(2)
        coIndicator!!.getLocationOnScreen(location)
        val tabScreenY = location[1]
        val tabHeight = coIndicator!!.height
        val tabBottomY = tabScreenY + tabHeight
        val screenHeight = resources.displayMetrics.heightPixels

        // 优化判断逻辑：三阶段切换，实现预显示机制，支持动态阈值
        // 基础阈值
        val basePreShowThreshold = screenHeight - (tabHeight * 0.8f).toInt()
        val baseFullShowThreshold = screenHeight - tabHeight

        // 应用动态阈值算法
        val (dynamicPreShowThreshold, dynamicFullShowThreshold) = calculateDynamicThresholds(basePreShowThreshold)

        // 最终阈值
        val preShowThreshold = dynamicPreShowThreshold
        val fullShowThreshold = dynamicFullShowThreshold

        // 隐藏阈值：当原生Tab刚开始进入屏幕时就隐藏吸底Tab（提前触发）
        val hideThreshold = screenHeight - (tabHeight * 0.2f).toInt() // 原生Tab显示20%时就隐藏吸底Tab

        // 预加载阈值：当原生Tab滚动到90%可见时预创建
        val preCreateThreshold = screenHeight - (tabHeight * 0.9f).toInt()

        // 计算切换状态（四阶段逻辑：预创建 -> 预显示 -> 完全显示 -> 隐藏）
        val shouldPreCreate = tabBottomY > preCreateThreshold && !isPreCreated  // 预创建
        val shouldPreShow = tabBottomY > preShowThreshold && tabBottomY <= fullShowThreshold  // 预显示区间
        val shouldFullShow = tabBottomY > fullShowThreshold  // 完全显示
        val shouldHide = tabBottomY <= hideThreshold // 隐藏

        // 添加安全检查：确保不是初始状态的异常位置
        val isValidPosition = tabScreenY > -tabHeight && tabScreenY < screenHeight * 2

        // 获取当前时间，用于防抖动
        val currentTime = System.currentTimeMillis()
        val isDebounceTimeElapsed = currentTime - lastSwitchTime > switchDebounceDelay

        when {
            shouldPreCreate && isValidPosition -> {
                // 原生Tab滚动到90%可见时，预创建吸底Tab组件
                val visiblePercent = (maxOf(0, screenHeight - tabScreenY).toFloat() / tabHeight * 100).toInt()

                preCreateStickyBottomTab()
            }
            shouldHide && (isBottomTabVisible || isPreShowing) && isDebounceTimeElapsed -> {
                // 原生Tab刚开始进入屏幕（显示约20%）时，提前隐藏吸底Tab，避免重叠
                val visiblePercent = (maxOf(0, screenHeight - tabScreenY).toFloat() / tabHeight * 100).toInt()

                hideStickyBottomTab()
                lastSwitchTime = currentTime  // 更新切换时间
            }
            shouldPreShow && !isBottomTabVisible && !isPreShowing && isValidPosition && isDebounceTimeElapsed -> {
                // 原生Tab滚动到80%可见时，开始预显示
                val visiblePercent = (maxOf(0, screenHeight - tabScreenY).toFloat() / tabHeight * 100).toInt()

                preShowStickyBottomTab()
                lastSwitchTime = currentTime  // 更新切换时间
            }
            shouldFullShow && isPreShowing && isDebounceTimeElapsed -> {
                // 原生Tab大部分滚出屏幕时，从预显示转为完全显示
                fullShowStickyBottomTab()
                lastSwitchTime = currentTime  // 更新切换时间
            }
            shouldFullShow && !isBottomTabVisible && !isPreShowing && isValidPosition && isDebounceTimeElapsed -> {
                // 直接跳到完全显示（跳过预显示阶段，适用于快速滚动）
                showStickyBottomTab()
                lastSwitchTime = currentTime  // 更新切换时间
            }
        }
    }

    /**
     * 检查并显示初始吸底Tab - 只有在原生Tab（CoIndicator）真正不可见时才显示初始吸底Tab
     */
    private fun checkAndShowInitialStickyBottomTab() {
        if (coIndicator == null || coIndicator!!.height <= 0) {
            // 如果原生Tab还没有布局完成，延迟检查
            Log.d(TAG, "原生Tab布局未完成，延迟检查初始吸底Tab显示")
            clLayout?.postDelayed({
                checkAndShowInitialStickyBottomTab()
            }, 100)
            return
        }

        // 检查原生Tab的位置
        val location = IntArray(2)
        coIndicator!!.getLocationOnScreen(location)
        val tabScreenY = location[1]
        val tabHeight = coIndicator!!.height
        val tabBottomY = tabScreenY + tabHeight
        val screenHeight = resources.displayMetrics.heightPixels

        // 判断原生Tab的可见性状态
        val isTabCompletelyVisible = tabScreenY >= 0 && tabBottomY <= screenHeight
        val isTabCompletelyHidden = tabScreenY >= screenHeight || tabBottomY <= 0
        val isTabPartiallyVisible = !isTabCompletelyVisible && !isTabCompletelyHidden

        Log.d(TAG, "初始Tab位置检测 - tabScreenY: $tabScreenY, tabHeight: $tabHeight, screenHeight: $screenHeight")
        Log.d(TAG, "可见性状态 - 完全可见: $isTabCompletelyVisible, 部分可见: $isTabPartiallyVisible, 完全隐藏: $isTabCompletelyHidden")

        // 只有当原生Tab完全不可见时才显示吸底Tab
        if (isTabCompletelyHidden && !isAnimating && !isBottomTabVisible) {
            Log.d(TAG, "原生Tab完全不可见，显示初始吸底Tab（无动画）")
            showStickyBottomTabWithoutAnimation()
        } else {
            Log.d(TAG, "原生Tab可见或部分可见，不显示初始吸底Tab")
        }
    }

    /**
     * 启动时显示初始吸底Tab（无动画）
     */
    private fun showInitialStickyBottomTab() {
        clLayout?.post {
            clLayout?.postDelayed({
                if (!isAnimating && !isBottomTabVisible) {
                    Log.d(TAG, "显示初始吸底Tab（无动画）")
                    showStickyBottomTabWithoutAnimation()
                }
            }, 100) // 短暂延迟确保布局完成
        }
    }

    /**
     * 显示吸底Tab（无动画，用于初始显示）
     */
    private fun showStickyBottomTabWithoutAnimation() {
        if (isAnimating || isBottomTabVisible || stickyBottomTabContainer != null || clLayout == null) return

        Log.d(TAG, "开始创建初始吸底Tab（无动画）")
        isBottomTabVisible = true

        // 创建吸底Tab容器和CoIndicator
        stickyBottomTabContainer = createStickyTabContainer()
        stickyCoIndicator = createStickyCoIndicator()
        stickyBottomTabContainer?.addView(stickyCoIndicator)


        // 添加到CoordinatorLayout底部，考虑导航栏适配
        val layoutParams = CoordinatorLayout.LayoutParams(
            CoordinatorLayout.LayoutParams.MATCH_PARENT,
            CoordinatorLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.BOTTOM
            bottomMargin = actualBottomMargin
        }

        clLayout?.addView(stickyBottomTabContainer, layoutParams)

        // 直接显示，无动画，确保位置正确
        stickyBottomTabContainer?.let { container ->
            container.translationY = 0f
            container.alpha = 1f
            Log.d(TAG, "初始吸底Tab显示完成 - 位置: translationY=${container.translationY}, alpha=${container.alpha}")
            Log.d(TAG, "容器布局参数 - bottomMargin=$actualBottomMargin")
        }
    }

    /**
     * 显示吸底Tab
     */
    private fun showStickyBottomTab() {
        if (isAnimating || isBottomTabVisible || stickyBottomTabContainer != null || clLayout == null) return

        Log.d(TAG, "开始创建吸底Tab")
        isAnimating = true
        isBottomTabVisible = true

        // 创建吸底Tab容器和CoIndicator
        stickyBottomTabContainer = createStickyTabContainer()
        stickyCoIndicator = createStickyCoIndicator()
        stickyBottomTabContainer?.addView(stickyCoIndicator)
        stickyBottomTabContainer?.addView(ImageView(context).apply {
            // 设置图片资源（替换为实际资源）
            setImageResource(R.drawable.home_refresh_up_icon)

            // 创建布局参数并设置右侧间距
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT,
            ).apply {
                // 设置16dp的右侧间距
                marginEnd = dp2px(16f)
                gravity = Gravity.END or Gravity.CENTER_VERTICAL

            }
            setOnClickListener {
                hideStickyBottomTabWithCallback {
                    // 吸底Tab隐藏动画完成后，再执行滚动动画
                    Log.d(TAG, "吸底Tab隐藏完成，开始滚动到原始Tab位置")
                    performScrollToPosition()
                }
            }
        })

        updateStickyCoIndicatorRedPoint(4,showRedPoint)
        // 添加到CoordinatorLayout底部，考虑导航栏适配
        val layoutParams = CoordinatorLayout.LayoutParams(
            CoordinatorLayout.LayoutParams.MATCH_PARENT,
            CoordinatorLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.BOTTOM
            bottomMargin = actualBottomMargin
        }

        clLayout?.addView(stickyBottomTabContainer, layoutParams)

        // 优化的入场动画：从原生Tab位置开始，实现视觉连续性
        stickyBottomTabContainer?.let { container ->
            // 获取原生Tab位置，实现视觉连续性
            val (nativeTabX, nativeTabY) = getNativeTabScreenPosition()
            val containerLocation = IntArray(2)
            container.getLocationOnScreen(containerLocation)
            val deltaY = nativeTabY - containerLocation[1]

            // 初始状态：从原生Tab位置开始
            val initialTranslationY = if (deltaY != 0) deltaY.toFloat() else dp2px(8f).toFloat()
            container.translationY = initialTranslationY
            container.alpha = 0f
            container.scaleY = 1f  // 移除缩放效果，简化动画

            Log.d(TAG, "视觉连续性动画 - 原生Tab位置: ($nativeTabX, $nativeTabY), 容器位置: (${containerLocation[0]}, ${containerLocation[1]}), 初始位移: $initialTranslationY")

            container.animate()
                .translationY(0f) // 确保最终位置是0，紧贴底部
                .alpha(1f)
                .setDuration(animationDuration)
                .setInterpolator(AccelerateDecelerateInterpolator()) // 使用平滑插值器
                .setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        // 确保动画结束后位置完全正确
                        container.translationY = 0f
                        container.alpha = 1f
                        isAnimating = false
                        Log.d(TAG, "吸底Tab入场动画完成，位置已校正")
                        Log.d(TAG, "最终位置: translationY=${container.translationY}, alpha=${container.alpha}")
                        Log.d(TAG, "容器布局参数 - bottomMargin=$actualBottomMargin")
                    }
                    override fun onAnimationCancel(animation: Animator) {
                        // 动画取消时也要确保位置正确
                        container.translationY = 0f
                        container.alpha = 1f
                        isAnimating = false
                        Log.d(TAG, "吸底Tab入场动画取消，位置已校正")
                    }
                })
                .start()
        }
    }

    /**
     * 预创建吸底Tab组件 - 减少显示延迟
     */
    private fun preCreateStickyBottomTab() {
        if (isPreCreated || preCreatedContainer != null) return

        Log.d(TAG, "预创建吸底Tab组件")
        isPreCreated = true

        try {
            // 预创建容器和CoIndicator，但不添加到布局中
            preCreatedContainer = createStickyTabContainer()
            preCreatedCoIndicator = createStickyCoIndicator()
            preCreatedContainer?.addView(preCreatedCoIndicator)

            Log.d(TAG, "吸底Tab组件预创建完成")
        } catch (e: Exception) {
            Log.e(TAG, "预创建吸底Tab组件失败", e)
            isPreCreated = false
            preCreatedContainer = null
            preCreatedCoIndicator = null
        }
    }

    /**
     * 计算滚动速度用于动态阈值调整
     */
    private fun calculateScrollVelocity() {
        try {
            val currentScrollY = coIndicator?.let { indicator ->
                val location = IntArray(2)
                indicator.getLocationOnScreen(location)
                location[1]
            } ?: 0

            val currentTime = System.currentTimeMillis()

            if (lastScrollTime > 0) {
                val deltaY = currentScrollY - lastScrollY
                val deltaTime = currentTime - lastScrollTime

                if (deltaTime > 0) {
                    scrollVelocity = Math.abs(deltaY.toFloat() / deltaTime)
                    Log.v(TAG, "滚动速度: ${scrollVelocity}px/ms, deltaY: ${deltaY}px, deltaTime: ${deltaTime}ms")
                }
            }

            lastScrollY = currentScrollY
            lastScrollTime = currentTime

        } catch (e: Exception) {
            Log.w(TAG, "计算滚动速度失败", e)
            scrollVelocity = 0f
        }
    }

    /**
     * 基于滚动速度计算动态阈值
     */
    private fun calculateDynamicThresholds(baseThreshold: Int): Pair<Int, Int> {
        // 根据滚动速度调整阈值
        // 快速滚动时提前显示，慢速滚动时延后显示
        val tabHeight = coIndicator!!.height
        val velocityFactor = Math.min(scrollVelocity / 2f, 0.3f)  // 最大调整30%
        val adjustment = (tabHeight * velocityFactor).toInt()

        val adjustedPreShowThreshold = baseThreshold + adjustment
        val adjustedFullShowThreshold = baseThreshold + adjustment

        Log.v(TAG, "动态阈值调整 - 滚动速度: ${scrollVelocity}px/ms, 调整量: ${adjustment}px")

        return Pair(adjustedPreShowThreshold, adjustedFullShowThreshold)
    }

    /**
     * 获取原生Tab的屏幕位置，用于视觉连续性动画
     */
    private fun getNativeTabScreenPosition(): Pair<Int, Int> {
        return try {
            coIndicator?.let { indicator ->
                val location = IntArray(2)
                indicator.getLocationOnScreen(location)
                Pair(location[0], location[1])  // x, y坐标
            } ?: Pair(0, 0)
        } catch (e: Exception) {
            Log.w(TAG, "获取原生Tab位置失败", e)
            Pair(0, 0)
        }
    }

    /**
     * 预显示吸底Tab - 低透明度显示，建立视觉连接，使用预创建组件
     */
    private fun preShowStickyBottomTab() {
        if (isAnimating || isBottomTabVisible || isPreShowing) return

        Log.d(TAG, "开始预显示吸底Tab")
        isAnimating = true
        isPreShowing = true

        // 使用预创建的组件，如果没有则现场创建
        if (isPreCreated && preCreatedContainer != null && preCreatedCoIndicator != null) {
            Log.d(TAG, "使用预创建的组件进行预显示")
            stickyBottomTabContainer = preCreatedContainer
            stickyCoIndicator = preCreatedCoIndicator
            // 清空预创建状态
            preCreatedContainer = null
            preCreatedCoIndicator = null
            isPreCreated = false
        } else {
            Log.d(TAG, "预创建组件不可用，现场创建")
            stickyBottomTabContainer = createStickyTabContainer()
            stickyCoIndicator = createStickyCoIndicator()
            stickyBottomTabContainer?.addView(stickyCoIndicator)

        }

        // 添加到CoordinatorLayout底部
        val layoutParams = CoordinatorLayout.LayoutParams(
            CoordinatorLayout.LayoutParams.MATCH_PARENT,
            CoordinatorLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.BOTTOM
            bottomMargin = actualBottomMargin
        }

        clLayout?.addView(stickyBottomTabContainer, layoutParams)

        // 预显示动画：低透明度显示
        stickyBottomTabContainer?.let { container ->
            container.translationY = 0f  // 无位移
            container.alpha = 0f
        }
    }

    /**
     * 完全显示吸底Tab - 从预显示状态转为完全显示
     */
    private fun fullShowStickyBottomTab() {
        if (isAnimating || !isPreShowing || stickyBottomTabContainer == null) return

        Log.d(TAG, "吸底Tab从预显示转为完全显示")
        isAnimating = true
        isPreShowing = false
        isBottomTabVisible = true

        // 取消预显示动画
        preShowAnimator?.cancel()
        preShowAnimator = null

        // 完全显示动画
        stickyBottomTabContainer?.let { container ->
            container.animate()
                .alpha(1f)  // 完全不透明
                .setDuration(100L)  // 快速完全显示
                .setInterpolator(AccelerateDecelerateInterpolator())
                .setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        container.alpha = 1f
                        isAnimating = false
                        Log.d(TAG, "吸底Tab完全显示完成")
                    }
                    override fun onAnimationCancel(animation: Animator) {
                        container.alpha = 1f
                        isAnimating = false
                    }
                })
                .start()
        }
    }

    /**
     * 隐藏吸底Tab
     */
    private fun hideStickyBottomTab() {
        hideStickyBottomTabWithCallback(null)
    }

    /**
     * 隐藏吸底Tab - 带完成回调，防止异常显示，支持预显示状态
     */
    private fun hideStickyBottomTabWithCallback(onComplete: (() -> Unit)?) {
        Log.d(TAG, "=== 隐藏吸底Tab开始 ===")
        Log.d(TAG, "当前状态: isAnimating=$isAnimating, isBottomTabVisible=$isBottomTabVisible, isPreShowing=$isPreShowing")
        Log.d(TAG, "容器状态: stickyBottomTabContainer=${stickyBottomTabContainer != null}, clLayout=${clLayout != null}")

        // 处理预显示状态的隐藏
        if (isPreShowing) {
            Log.d(TAG, "取消预显示状态")
            preShowAnimator?.cancel()
            preShowAnimator = null
            isPreShowing = false
            isAnimating = false

            // 直接移除容器
            try {
                clLayout?.removeView(stickyBottomTabContainer)
                stickyBottomTabContainer = null
                stickyCoIndicator = null
                Log.d(TAG, "预显示状态已取消，容器已移除")
            } catch (e: Exception) {
                Log.e(TAG, "移除预显示容器时发生异常", e)
            }

            onComplete?.invoke()
            return
        }

        if (isAnimating || !isBottomTabVisible || stickyBottomTabContainer == null || clLayout == null) {
            Log.d(TAG, "无法执行隐藏动画，直接执行回调")
            // 如果无法执行隐藏动画，直接执行回调
            onComplete?.invoke()
            return
        }

        Log.d(TAG, "开始执行隐藏动画")
        isAnimating = true
        isBottomTabVisible = false
        isPreShowing = false  // 重置预显示状态

        val containerToRemove = stickyBottomTabContainer
        stickyBottomTabContainer = null
        stickyCoIndicator = null  // 清理CoIndicator引用
        preShowAnimator?.cancel()  // 取消可能存在的预显示动画
        preShowAnimator = null

        // 优化的退场动画：简化效果，只使用透明度和轻微位移
        containerToRemove?.animate()
            ?.translationY(dp2px(8f).toFloat())  // 轻微的8dp位移，与入场动画一致
            ?.alpha(0f)
            ?.setDuration(animationDuration)
            ?.setInterpolator(AccelerateDecelerateInterpolator()) // 使用平滑的插值器
            ?.setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    try {
                        clLayout?.removeView(containerToRemove)
                        Log.d(TAG, "吸底Tab退场动画完成，容器已移除")
                    } catch (e: Exception) {
                        Log.e(TAG, "移除吸底Tab时发生异常", e)
                    }
                    isAnimating = false

                    // 动画完成后执行回调
                    Log.d(TAG, "执行隐藏完成回调")
                    onComplete?.invoke()
                }
                override fun onAnimationCancel(animation: Animator) {
                    Log.d(TAG, "隐藏动画被取消")
                    isAnimating = false
                    // 动画取消时也执行回调，确保流程继续
                    onComplete?.invoke()
                }
            })
            ?.start()
    }

    /**
     * 创建吸底Tab容器 - 与原生CoIndicator样式一致
     */
    private fun createStickyTabContainer(): LinearLayout {
        return LinearLayout(requireContext()).apply {
            LayoutParams.MATCH_PARENT
            LayoutParams.MATCH_PARENT
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER_VERTICAL
            setBackgroundColor(Color.WHITE)
            elevation = 8f

        }
    }

    /**
     * dp转px工具方法
     */
    private fun dp2px(dp: Float): Int {
        return (dp * resources.displayMetrics.density + 0.5f).toInt()
    }

    /**
     * 创建吸底CoIndicator - 直接复用原生组件
     */
    private fun createStickyCoIndicator(): CoIndicator {
        Log.d(TAG, "开始创建吸底CoIndicator")

        // 创建新的CoIndicator实例
        val stickyCoIndicator = CoIndicator(requireContext()).apply {
            layoutParams = LinearLayout.LayoutParams(
                0,
                dp2px(40f) // 与原生CoIndicator相同的高度
            ).apply {
                weight = 1f
            }
        }

        // 使用与原生相同的初始化方法和参数
        activity?.let { activity ->
            tabs?.let { tabList ->
                Log.d(TAG, "使用原生IndicatorHelper初始化吸底CoIndicator")

                // 使用与原生完全相同的配置参数
                IndicatorHelper.initBottomNoLineIndicator(
                    activity,
                    tabList,
                    stickyCoIndicator,
                    0f, // indicatorYOffset
                    null, // 不绑定ViewPager，避免冲突
                    16f, // textSize
                    R.attr.Text_L1, // selColor
                    R.attr.Text_L3, // unSelColor
                    scaleSize = 20f,
                    isBold = true,
                    showIndicator = false,
                    onTabClick = object : TabClickListener {
                        override fun onTabClick(index: Int) {
                            handleStickyTabClick(index)
                        }
                    }
                )

                // 设置初始选中状态
                val currentPosition = mFeedViewPager?.currentItem ?: 0
                stickyCoIndicator.onPageSelected(currentPosition)
            }
        }

        return stickyCoIndicator
    }

    /**
     * 处理吸底Tab点击事件 - 优化动画执行顺序，防止异常显示
     */
    private fun handleStickyTabClick(position: Int) {
        Log.d(TAG, "=== 吸底Tab点击开始 ===")
        Log.d(TAG, "点击位置: position=$position")
        Log.d(TAG, "当前状态: isBottomTabVisible=$isBottomTabVisible, isAnimating=$isAnimating, isHandlingTabClick=$isHandlingTabClick")

        // 设置点击处理标志，防止滚动监听器干扰
        isHandlingTabClick = true

        // 切换ViewPager页面
        mFeedViewPager?.setCurrentItem(position, true)
        Log.d(TAG, "ViewPager页面已切换")

        // 同步原生CoIndicator的状态
        coIndicator?.onPageSelected(position)
        Log.d(TAG, "原生CoIndicator状态已同步")

        // 执行与原生Tab相同的业务逻辑
        val item = tabs?.get(position)
        if (0 == item?.type) {
            SensorsDataHelper.track("app_community_homepage_find_tab", hashMapOf<String, String>())
        } else if (2 == item?.type) {
            SensorsDataHelper.newTrack("app_community_kxtab_click", hashMapOf<String, String>())
        } else if (3 == item?.type) {
            SensorsDataHelper.newTrack(LiveTrackUtils.LIVE_SQUARE_SHOW)
        } else if (4 == item?.type) {
            SensorsDataHelper.newTrack("app_bulletin_tab_click", hashMapOf<String, String>())
            changeRedPoint(4, false)
            showRedPoint = false
        }
        Log.d(TAG, "业务逻辑执行完成")

        // 调整执行顺序：先隐藏吸底Tab，再执行滚动动画
        Log.d(TAG, "开始隐藏吸底Tab，然后执行滚动动画")

    }

    /**
     * 滚动到指定位置（默认屏幕1/3处）- 防止异常显示
     */
    private fun performScrollToPosition() {
        Log.d(TAG, "=== 滚动到指定位置开始 ===")
        Log.d(TAG, "当前状态: isAnimating=$isAnimating, isHandlingTabClick=$isHandlingTabClick")

        // 如果正在动画中，不执行滚动
        if (isAnimating) {
            Log.d(TAG, "正在动画中，跳过滚动")
            return
        }

        clLayout?.post {
            val screenHeight = resources.displayMetrics.heightPixels
            val targetY = (screenHeight * scrollToPosition).toInt()

            // 先完全展开AppBarLayout
            appBarLayout?.setExpanded(true, false)

            // 等待布局完成后计算需要的滚动距离
            clLayout?.post {
                val tabLocation = IntArray(2)
                coIndicator?.getLocationOnScreen(tabLocation)
                val currentTabY = tabLocation[1]

                // 计算需要向下滚动的距离
                val scrollDistance = currentTabY - targetY

                if (scrollDistance > 0) {
                    // 设置动画标志，防止滚动监听器干扰
                    isAnimating = true

                    // 使用AppBarLayout的Behavior来精确控制滚动
                    val behavior = (appBarLayout?.layoutParams as? CoordinatorLayout.LayoutParams)?.behavior
                    if (behavior is AppBarLayout.Behavior) {
                        val totalScrollRange = appBarLayout?.totalScrollRange ?: 0
                        val targetOffset = kotlin.math.min(totalScrollRange, scrollDistance)

                        // 使用动画平滑滚动到目标位置
                        val animator = ValueAnimator.ofInt(0, targetOffset)
                        animator.duration = 400
                        animator.interpolator = DecelerateInterpolator()
                        animator.addUpdateListener { animation ->
                            val offset = animation.animatedValue as Int
                            behavior.topAndBottomOffset = -offset
                            appBarLayout?.requestLayout()
                        }

                        // 添加动画结束监听器
                        animator.addListener(object : AnimatorListenerAdapter() {
                            override fun onAnimationEnd(animation: Animator) {
                                super.onAnimationEnd(animation)
                                isAnimating = false // 重置动画标志
                                isHandlingTabClick = false // 重置点击处理标志，恢复滚动监听
                                Log.d(TAG, "滚动动画完成，状态已重置")
                                Log.d(TAG, "=== 吸底Tab点击处理完成 ===")
                            }

                            override fun onAnimationCancel(animation: Animator) {
                                super.onAnimationCancel(animation)
                                isAnimating = false // 重置动画标志
                                isHandlingTabClick = false // 重置点击处理标志，恢复滚动监听
                                Log.d(TAG, "滚动动画被取消，状态已重置")
                            }
                        })

                        animator.start()
                        Log.d(TAG, "开始平滑滚动到目标位置")
                    } else {
                        isAnimating = false // 如果无法获取behavior，重置标志
                    }
                } else {
                    // 如果不需要滚动，重置状态（吸底Tab已在点击时隐藏）
                    isHandlingTabClick = false // 重置点击处理标志，恢复滚动监听
                    Log.d(TAG, "无需滚动，状态已重置")
                    Log.d(TAG, "=== 吸底Tab点击处理完成 ===")
                }

                Log.d(TAG, "滚动到指定位置处理完成")
            }
        }
    }

    /**
     * 同步吸底CoIndicator的选中状态
     */
    private fun syncStickyTabSelection(position: Int) {
        if (isBottomTabVisible && stickyCoIndicator != null) {
            stickyCoIndicator?.onPageSelected(position)
        }
    }

    /**
     * 更新吸底CoIndicator的红点状态
     */
    private fun updateStickyCoIndicatorRedPoint(position: Int, show: Boolean) {
        try {
            // 如果吸底CoIndicator正在显示，同步更新红点
            if (isBottomTabVisible && stickyCoIndicator != null) {
                (stickyCoIndicator?.navigator as? CommonNavigator)?.let { commonNavigator ->
                    val titleView = commonNavigator.getPagerTitleView(position) as? RedPointPagerTitleView
                    if (showRedPoint) {
                        titleView?.showPoint()
                    } else {
                        titleView?.hidePoint()
                    }
                }
            }
        } catch (e: Exception) {
        }
    }


}